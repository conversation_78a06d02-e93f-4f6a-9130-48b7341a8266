#!/usr/bin/env python3
"""
OCRFlux API快速测试脚本
"""

import requests
import base64
import json
from io import BytesIO
from PIL import Image

# 服务器配置
BASE_URL = "http://baizh.eu.cc:8002"
MODEL = "models/"  # 实际服务器上的模型名称
API_ENDPOINT = f"{BASE_URL}/v1/chat/completions"

def create_test_image():
    """创建一个简单的测试图像"""
    img = Image.new('RGB', (200, 100), color='white')
    # 添加一些文本（需要PIL支持字体，这里用简单的像素绘制）
    buffered = BytesIO()
    img.save(buffered, format="PNG")
    return base64.b64encode(buffered.getvalue()).decode('utf-8')

def test_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    try:
        # 发送一个简单的文本请求
        payload = {
            "model": MODEL,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10,
            "temperature": 0.0
        }
        
        response = requests.post(API_ENDPOINT, json=payload, timeout=10)
        
        if response.status_code == 200:
            print("✅ 服务器连接成功!")
            return True
        else:
            print(f"⚠️ 服务器响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_text_completion():
    """测试纯文本补全"""
    print("\n📝 测试文本补全...")
    
    payload = {
        "model": MODEL,
        "messages": [
            {"role": "user", "content": "请用中文简单介绍一下OCR技术。"}
        ],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    try:
        response = requests.post(API_ENDPOINT, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        content = result["choices"][0]["message"]["content"]
        
        print("✅ 文本补全成功!")
        print(f"📄 回答: {content}")
        return True
        
    except Exception as e:
        print(f"❌ 文本补全失败: {e}")
        return False

def test_multimodal():
    """测试多模态功能"""
    print("\n🖼️ 测试多模态功能...")
    
    # 创建测试图像
    test_image_b64 = create_test_image()
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "请描述这个图像的内容，并将其转换为markdown格式。"},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{test_image_b64}"}}
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 500
    }
    
    try:
        response = requests.post(API_ENDPOINT, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        content = result["choices"][0]["message"]["content"]
        
        print("✅ 多模态测试成功!")
        print(f"📄 结果: {content}")
        return True
        
    except Exception as e:
        print(f"❌ 多模态测试失败: {e}")
        return False

def test_with_real_image(image_path):
    """使用真实图像测试OCR功能"""
    print(f"\n📸 测试OCR功能 (图像: {image_path})...")
    
    try:
        with open(image_path, "rb") as img_file:
            image_b64 = base64.b64encode(img_file.read()).decode('utf-8')
        
        payload = {
            "model": MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请将这个页面转换为markdown格式，保持原有的结构和内容。"},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                    ]
                }
            ],
            "temperature": 0.0,
            "max_tokens": 4096
        }
        
        response = requests.post(API_ENDPOINT, json=payload, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        content = result["choices"][0]["message"]["content"]
        
        print("✅ OCR测试成功!")
        print(f"📄 OCR结果:\n{content}")
        
        # 保存结果到文件
        with open(f"ocr_result_{image_path.split('/')[-1]}.md", "w", encoding="utf-8") as f:
            f.write(content)
        print(f"💾 结果已保存到: ocr_result_{image_path.split('/')[-1]}.md")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 图像文件未找到: {image_path}")
        return False
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 OCRFlux API 快速测试")
    print(f"🌐 服务器: {BASE_URL}")
    print(f"🤖 模型: {MODEL}")
    print("=" * 50)
    
    # 测试连接
    if not test_connection():
        print("\n❌ 无法连接到服务器，请检查:")
        print("1. 服务器是否正在运行")
        print("2. 网络连接是否正常")
        print("3. 端口8002是否开放")
        return
    
    # 测试文本补全
    test_text_completion()
    
    # 测试多模态
    test_multimodal()
    
    # 如果有图像文件，测试OCR
    import os
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    if image_files:
        print(f"\n📁 发现图像文件: {image_files}")
        for img_file in image_files[:2]:  # 最多测试2个文件
            test_with_real_image(img_file)
    else:
        print("\n💡 提示: 在当前目录放置图像文件(.png, .jpg, .jpeg)可以测试OCR功能")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
