#!/usr/bin/env python3
"""
OCRFlux 题目解析器 - 最终优化版
直接输出结构化题目信息，不保存文件
"""

import json
import urllib.request
import base64
import os

# 服务器配置
SERVER_URL = "http://baizh.eu.cc:8002"
MODEL_NAME = "models/"
API_ENDPOINT = f"{SERVER_URL}/v1/chat/completions"

class QuestionParser:
    def __init__(self):
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'OCRFlux-QuestionParser/1.0'
        }
    
    def ocr_image(self, image_path):
        """对图像进行OCR识别，直接返回结构化数据"""
        try:
            # 编码图像
            with open(image_path, "rb") as image_file:
                image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        except Exception:
            return None
        
        # 构建结构化提示词
        prompt = """请分析这张图片中的选择题内容，并按照以下JSON格式输出每道题目的信息：

{
  "questions": [
    {
      "题号": "题目编号（如5、8、6/619等）",
      "内容": "题目问题描述部分",
      "选项A": "选项A的具体内容",
      "选项B": "选项B的具体内容", 
      "选项C": "选项C的具体内容",
      "选项D": "选项D的具体内容",
      "正确答案": "正确答案字母（通过绿色背景或标准答案标注识别）"
    }
  ]
}

注意事项：
1. 仔细识别题目编号，保持原格式
2. 题目内容不要包含"本小题X分"等评分信息
3. 正确答案通过以下方式识别：
   - 查找"标准答案：X"的文字标注
   - 或识别绿色背景的选项
4. 如果图片中有多道题目，请在questions数组中包含所有题目
5. 只输出JSON格式，不要其他说明文字"""

        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                    ]
                }
            ],
            "temperature": 0.0,
            "max_tokens": 4096
        }
        
        try:
            data = json.dumps(payload).encode('utf-8')
            req = urllib.request.Request(API_ENDPOINT, data=data, headers=self.headers)
            
            with urllib.request.urlopen(req, timeout=120) as response:
                response_data = response.read().decode('utf-8')
                result = json.loads(response_data)
                model_output = result['choices'][0]['message']['content']
                print(f"模型输出内容: {model_output}")
                return model_output
                
        except Exception:
            return None
    
    def parse_questions(self, ocr_response):
        """解析OCR响应，提取题目信息"""
        try:
            # 清理响应文本，提取JSON部分
            json_text = ocr_response.strip()
            if json_text.startswith('```json'):
                json_text = json_text.replace('```json', '').replace('```', '').strip()
            elif json_text.startswith('```'):
                json_text = json_text.replace('```', '').strip()

            # 解析JSON
            result_data = json.loads(json_text)

            # 处理两种可能的格式
            if isinstance(result_data, list):
                # 直接是题目数组
                return result_data
            elif isinstance(result_data, dict) and 'questions' in result_data:
                # 包含questions字段的对象
                return result_data['questions']
            else:
                return []

        except:
            return []
    
    def clean_option(self, option_text):
        """清理选项文本，移除前缀"""
        if not option_text or option_text == 'N/A':
            return option_text

        # 移除 "A.", "B.", "C.", "D." 等前缀
        import re
        cleaned = re.sub(r'^[ABCD]\.?\s*', '', option_text.strip())
        return cleaned if cleaned else option_text

    def format_output(self, questions):
        """格式化输出结果"""
        for q in questions:
            print(f"题号: {q.get('题号', 'N/A')}")
            print(f"内容: {q.get('内容', 'N/A')}")
            print(f"选项A: {self.clean_option(q.get('选项A', 'N/A'))}")
            print(f"选项B: {self.clean_option(q.get('选项B', 'N/A'))}")
            print(f"选项C: {self.clean_option(q.get('选项C', 'N/A'))}")
            print(f"选项D: {self.clean_option(q.get('选项D', 'N/A'))}")
            print(f"正确答案: {q.get('正确答案', 'N/A')}")
            print("-" * 50)
    
    def process_image(self, image_path):
        """处理单个图像文件"""
        print(f"🚀 处理图像: {image_path}")
        
        # OCR识别
        ocr_response = self.ocr_image(image_path)
        if not ocr_response:
            return []
        
        # 解析题目
        questions = self.parse_questions(ocr_response)
        
        if questions:
            print(f"✅ 成功解析 {len(questions)} 道题目")
            return questions
        else:
            return []
    
    def process_folder(self, folder_path="pic"):
        """处理文件夹中的所有图像"""
        # 检查文件夹
        if not os.path.exists(folder_path):
            print(f"❌ 未找到 {folder_path} 文件夹")
            return []
        
        # 查找图像文件
        try:
            all_files = os.listdir(folder_path)
            image_files = [f for f in all_files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            image_paths = [os.path.join(folder_path, f) for f in image_files]
        except Exception:
            print(f"❌ 读取文件夹失败")
            return []
        
        if not image_files:
            print(f"❌ 在 {folder_path} 文件夹中未找到图像文件")
            return []
        
        print(f"📁 发现 {len(image_files)} 个图像文件")
        print("=" * 60)
        
        all_questions = []
        
        # 处理每个图像文件
        for image_path in image_paths:
            questions = self.process_image(image_path)
            print(f"-------------{questions}")
            if questions:
                self.format_output(questions)
                all_questions.extend(questions)
        
        return all_questions

def main():
    """主函数"""
    parser = QuestionParser()
    
    # 处理pic文件夹中的所有图像
    all_questions = parser.process_folder("pic")
    
    if all_questions:
        print(f"\n🎉 总共处理了 {len(all_questions)} 道题目")
    else:
        print("❌ 未解析出任何题目")

if __name__ == "__main__":
    main()
