#!/usr/bin/env python3
"""
使用OpenAI SDK调用OCRFlux API的示例
需要安装: pip install openai
"""

import base64
import os
from pathlib import Path

try:
    from openai import OpenAI
except ImportError:
    print("❌ 请先安装OpenAI SDK: pip install openai")
    exit(1)

# 配置OCRFlux客户端
client = OpenAI(
    api_key="dummy-key",  # OCRFlux不需要真实的API key
    base_url="http://baizh.eu.cc:8002/v1"
)

MODEL = "models/"

def encode_image(image_path):
    """将图像编码为base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def test_text_completion():
    """测试文本补全功能"""
    print("📝 测试文本补全...")
    
    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "user", "content": "请用中文简单介绍一下OCR技术，限制在100字以内。"}
            ],
            temperature=0.1,
            max_tokens=200
        )
        
        content = response.choices[0].message.content
        print("✅ 文本补全成功!")
        print(f"📄 回答: {content}")
        return True
        
    except Exception as e:
        print(f"❌ 文本补全失败: {e}")
        return False

def test_ocr_with_image(image_path):
    """测试OCR功能"""
    print(f"📸 测试OCR功能 (图像: {image_path})...")
    
    try:
        # 编码图像
        image_base64 = encode_image(image_path)
        
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请将这个页面转换为markdown格式，保持原有的结构和内容。"},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                    ]
                }
            ],
            temperature=0.0,
            max_tokens=4096
        )
        
        content = response.choices[0].message.content
        print("✅ OCR测试成功!")
        print(f"📄 OCR结果:\n{content}")
        
        # 保存结果
        output_file = f"ocr_result_{Path(image_path).stem}.md"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(content)
        print(f"💾 结果已保存到: {output_file}")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 图像文件未找到: {image_path}")
        return False
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return False

def test_multimodal_analysis():
    """测试多模态分析功能"""
    print("🔍 测试多模态分析...")
    
    try:
        # 创建一个简单的测试图像
        from PIL import Image
        from io import BytesIO
        
        # 创建一个包含文本的简单图像
        img = Image.new('RGB', (400, 200), color='white')
        buffered = BytesIO()
        img.save(buffered, format="PNG")
        image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请分析这个图像的内容，包括布局、颜色、可能的用途等。"},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                    ]
                }
            ],
            temperature=0.2,
            max_tokens=500
        )
        
        content = response.choices[0].message.content
        print("✅ 多模态分析成功!")
        print(f"📄 分析结果: {content}")
        return True
        
    except Exception as e:
        print(f"❌ 多模态分析失败: {e}")
        return False

def test_streaming_response():
    """测试流式响应"""
    print("🌊 测试流式响应...")
    
    try:
        stream = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "user", "content": "请详细解释OCR技术的工作原理和应用场景。"}
            ],
            temperature=0.3,
            max_tokens=500,
            stream=True
        )
        
        print("✅ 流式响应开始:")
        print("📄 内容: ", end="", flush=True)
        
        full_content = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_content += content
        
        print("\n✅ 流式响应完成!")
        return True
        
    except Exception as e:
        print(f"❌ 流式响应失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 OCRFlux OpenAI SDK 测试")
    print(f"🌐 服务器: http://baizh.eu.cc:8002")
    print(f"🤖 模型: {MODEL}")
    print("=" * 60)
    
    # 1. 测试文本补全
    test_text_completion()
    print()
    
    # 2. 测试多模态分析
    test_multimodal_analysis()
    print()
    
    # 3. 测试流式响应
    test_streaming_response()
    print()
    
    # 4. 如果有图像文件，测试OCR
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    if image_files:
        print(f"📁 发现图像文件: {image_files}")
        for img_file in image_files[:2]:  # 最多测试2个文件
            test_ocr_with_image(img_file)
            print()
    else:
        print("💡 提示: 在当前目录放置图像文件(.png, .jpg, .jpeg)可以测试OCR功能")
        print()
    
    print("🎉 所有测试完成!")
    print("\n📚 更多使用示例:")
    print("- 查看 README_API_TEST.md 了解详细API使用方法")
    print("- 运行 quick_test.py 进行快速功能测试")
    print("- 运行 test_curl.sh 使用curl进行测试")

if __name__ == "__main__":
    main()
