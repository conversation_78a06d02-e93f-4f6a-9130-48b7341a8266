#!/usr/bin/env python3
"""
OCRFlux HTTP请求测试脚本
使用标准库和requests进行API调用测试
"""

import json
import time
import base64
from io import BytesIO
from pathlib import Path

try:
    import requests
    from PIL import Image
except ImportError as e:
    print(f"❌ 缺少依赖库: {e}")
    print("请安装: pip install requests pillow")
    exit(1)

# 服务器配置
SERVER_URL = "http://baizh.eu.cc:8002"
MODEL_NAME = "models/"
API_ENDPOINT = f"{SERVER_URL}/v1/chat/completions"

class OCRFluxTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'OCRFlux-Tester/1.0'
        })
    
    def test_server_health(self):
        """测试服务器健康状态"""
        print("🔍 检查服务器状态...")
        try:
            response = self.session.get(f"{SERVER_URL}/health", timeout=10)
            if response.status_code == 200:
                print("✅ 服务器健康检查通过")
                return True
            else:
                print(f"⚠️ 服务器健康检查异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False
    
    def get_available_models(self):
        """获取可用模型列表"""
        print("📋 获取可用模型...")
        try:
            response = self.session.get(f"{SERVER_URL}/v1/models", timeout=10)
            if response.status_code == 200:
                models_data = response.json()
                models = [model['id'] for model in models_data.get('data', [])]
                print(f"✅ 可用模型: {models}")
                return models
            else:
                print(f"❌ 获取模型列表失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取模型列表异常: {e}")
            return []
    
    def test_text_chat(self, message="你好，请介绍一下你自己"):
        """测试纯文本对话"""
        print(f"💬 测试文本对话: {message[:30]}...")
        
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {"role": "user", "content": message}
            ],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            start_time = time.time()
            response = self.session.post(API_ENDPOINT, json=payload, timeout=60)
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ 文本对话成功 (耗时: {elapsed_time:.2f}秒)")
                print(f"📝 回复: {content[:200]}{'...' if len(content) > 200 else ''}")
                return True, content
            else:
                print(f"❌ 文本对话失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False, None
                
        except Exception as e:
            print(f"❌ 文本对话异常: {e}")
            return False, None
    
    def create_test_image(self, width=300, height=200, text="OCR测试"):
        """创建测试图像"""
        try:
            # 创建白色背景图像
            img = Image.new('RGB', (width, height), color='white')
            
            # 转换为base64
            buffered = BytesIO()
            img.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
            
            return img_base64
        except Exception as e:
            print(f"❌ 创建测试图像失败: {e}")
            return None
    
    def encode_image_file(self, image_path):
        """编码图像文件为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ 编码图像文件失败: {e}")
            return None
    
    def test_ocr_with_image(self, image_path=None, prompt="请将这个图像转换为markdown格式"):
        """测试OCR功能"""
        if image_path:
            print(f"📸 测试OCR功能 (图像文件: {image_path})...")
            image_base64 = self.encode_image_file(image_path)
        else:
            print("📸 测试OCR功能 (测试图像)...")
            image_base64 = self.create_test_image()
        
        if not image_base64:
            return False, None
        
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                    ]
                }
            ],
            "temperature": 0.0,
            "max_tokens": 4096
        }
        
        try:
            start_time = time.time()
            response = self.session.post(API_ENDPOINT, json=payload, timeout=120)
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ OCR测试成功 (耗时: {elapsed_time:.2f}秒)")
                print(f"📄 OCR结果: {content[:300]}{'...' if len(content) > 300 else ''}")
                
                # 保存结果到文件
                if image_path:
                    output_file = f"ocr_result_{Path(image_path).stem}.md"
                else:
                    output_file = "ocr_result_test.md"
                
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(content)
                print(f"💾 结果已保存到: {output_file}")
                
                return True, content
            else:
                print(f"❌ OCR测试失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False, None
                
        except Exception as e:
            print(f"❌ OCR测试异常: {e}")
            return False, None
    
    def test_batch_requests(self, count=3):
        """测试批量请求"""
        print(f"🔄 测试批量请求 ({count}个)...")
        
        success_count = 0
        total_time = 0
        
        for i in range(count):
            print(f"  请求 {i+1}/{count}...")
            success, _ = self.test_text_chat(f"这是第{i+1}个测试请求，请简短回复")
            if success:
                success_count += 1
            time.sleep(1)  # 避免请求过于频繁
        
        print(f"✅ 批量测试完成: {success_count}/{count} 成功")
        return success_count == count
    
    def test_different_parameters(self):
        """测试不同参数设置"""
        print("⚙️ 测试不同参数设置...")
        
        test_cases = [
            {"temperature": 0.0, "max_tokens": 100, "name": "低温度短回复"},
            {"temperature": 0.5, "max_tokens": 200, "name": "中等温度中等回复"},
            {"temperature": 0.9, "max_tokens": 300, "name": "高温度长回复"},
        ]
        
        for case in test_cases:
            print(f"  测试: {case['name']}")
            
            payload = {
                "model": MODEL_NAME,
                "messages": [{"role": "user", "content": "请简单介绍人工智能"}],
                "temperature": case["temperature"],
                "max_tokens": case["max_tokens"]
            }
            
            try:
                response = self.session.post(API_ENDPOINT, json=payload, timeout=60)
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    print(f"    ✅ 成功 (长度: {len(content)}字符)")
                else:
                    print(f"    ❌ 失败: {response.status_code}")
            except Exception as e:
                print(f"    ❌ 异常: {e}")
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🚀 OCRFlux HTTP API 全面测试")
        print(f"🌐 服务器: {SERVER_URL}")
        print(f"🤖 模型: {MODEL_NAME}")
        print("=" * 60)
        
        # 1. 服务器健康检查
        if not self.test_server_health():
            print("❌ 服务器不可用，终止测试")
            return
        print()
        
        # 2. 获取模型列表
        self.get_available_models()
        print()
        
        # 3. 文本对话测试
        self.test_text_chat("请用中文简单介绍OCR技术")
        print()
        
        # 4. OCR功能测试
        self.test_ocr_with_image()
        print()
        
        # 5. 查找并测试真实图像
        image_files = list(Path('.').glob('*.png')) + list(Path('.').glob('*.jpg')) + list(Path('.').glob('*.jpeg'))
        if image_files:
            print(f"📁 发现图像文件: {[f.name for f in image_files[:3]]}")
            for img_file in image_files[:2]:  # 最多测试2个文件
                self.test_ocr_with_image(str(img_file))
                print()
        
        # 6. 参数测试
        self.test_different_parameters()
        print()
        
        # 7. 批量请求测试
        self.test_batch_requests(3)
        print()
        
        print("🎉 全面测试完成!")
        print("\n📊 测试总结:")
        print("- 如果所有测试都通过，说明OCRFlux API工作正常")
        print("- 可以在你的应用中使用相同的HTTP请求格式")
        print("- API完全兼容OpenAI格式")

def main():
    """主函数"""
    tester = OCRFluxTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
