#!/usr/bin/env python3
"""
OCRFlux 题目解析器
从OCR结果中提取题号、内容、选项A/B/C/D、正确答案
"""

import json
import re
import urllib.request
import urllib.parse
import base64
from typing import List, Dict, Optional

# 服务器配置
SERVER_URL = "http://baizh.eu.cc:8002"
MODEL_NAME = "models/"
API_ENDPOINT = f"{SERVER_URL}/v1/chat/completions"

class QuestionParser:
    def __init__(self):
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'OCRFlux-QuestionParser/1.0'
        }
    
    def make_request(self, url, data=None, timeout=120):
        """发送HTTP请求"""
        try:
            if data:
                data = json.dumps(data).encode('utf-8')
            
            req = urllib.request.Request(url, data=data, headers=self.headers)
            
            with urllib.request.urlopen(req, timeout=timeout) as response:
                response_data = response.read().decode('utf-8')
                return response.status, response_data
                
        except Exception as e:
            return None, str(e)
    
    def ocr_image(self, image_path):
        """对图像进行OCR识别"""
        print(f"📸 正在识别图像: {image_path}")
        
        try:
            # 编码图像
            with open(image_path, "rb") as image_file:
                image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ 读取图像失败: {e}")
            return None
        
        # 构建OCR请求 - 使用结构化提示词
        prompt = """请分析这张图片中的选择题内容，并按照以下JSON格式输出每道题目的信息：

{
  "questions": [
    {
      "题号": "题目编号（如5、8、6/619等）",
      "内容": "题目问题描述部分",
      "选项A": "选项A的具体内容",
      "选项B": "选项B的具体内容",
      "选项C": "选项C的具体内容",
      "选项D": "选项D的具体内容",
      "正确答案": "正确答案字母（通过绿色背景或标准答案标注识别）"
    }
  ]
}

注意事项：
1. 仔细识别题目编号，保持原格式
2. 题目内容不要包含"本小题X分"等评分信息
3. 正确答案通过以下方式识别：
   - 查找"标准答案：X"的文字标注
   - 或识别绿色背景的选项
4. 如果图片中有多道题目，请在questions数组中包含所有题目
5. 只输出JSON格式，不要其他说明文字"""

        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/png;base64,{image_base64}"}
                        }
                    ]
                }
            ],
            "temperature": 0.0,
            "max_tokens": 4096
        }
        
        try:
            status, response = self.make_request(API_ENDPOINT, payload)
            print(status,response)
            if status == 200:
                print('-----------')
                result = json.loads(response)
                content = result['choices'][0]['message']['content']
                print(f"✅ OCR识别成功---------{content}")
                # 直接返回模型的JSON响应
                return content
            else:
                print(f"❌ OCR请求失败: {status}")
                return None
                
        except Exception as e:
            print(f"❌ OCR请求异常: {e}")
            return None
    
    # 注意：parse_questions, extract_question_info, extract_option 函数已删除
    # 因为现在直接使用模型输出的结构化JSON数据
    
    def format_output(self, questions):
        """格式化输出结果"""
        for q in questions:
            print(f"题号: {q['题号']}")
            print(f"内容: {q['内容']}")
            print(f"选项A: {q['选项A']}")
            print(f"选项B: {q['选项B']}")
            print(f"选项C: {q['选项C']}")
            print(f"选项D: {q['选项D']}")
            print(f"正确答案: {q['正确答案']}")
            print("-" * 50)
    
    def process_image(self, image_path):
        """处理单个图像文件"""
        print(f"🚀 处理图像: {image_path}")

        # OCR识别 - 直接获取结构化结果
        ocr_response = self.ocr_image(image_path)
        if not ocr_response:
            return []

        # 解析JSON响应
        try:
            # 清理响应文本，提取JSON部分
            json_text = ocr_response.strip()
            if json_text.startswith('```json'):
                json_text = json_text.replace('```json', '').replace('```', '').strip()
            elif json_text.startswith('```'):
                json_text = json_text.replace('```', '').strip()

            # 解析JSON
            result_data = json.loads(json_text)
            questions = result_data.get('questions', [])

            if questions:
                print(f"✅ 成功解析 {len(questions)} 道题目")
                return questions
            else:
                return []

        except json.JSONDecodeError:
            return []
        except Exception:
            return []

def main():
    """主函数"""
    import os

    parser = QuestionParser()

    # 检查pic文件夹
    pic_folder = "pic"
    if not os.path.exists(pic_folder):
        print(f"❌ 未找到 {pic_folder} 文件夹")
        return

    # 查找pic文件夹中的图像文件
    try:
        all_files = os.listdir(pic_folder)
        image_files = [f for f in all_files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        image_paths = [os.path.join(pic_folder, f) for f in image_files]
    except Exception as e:
        print(f"❌ 读取pic文件夹失败: {e}")
        return

    if not image_files:
        print(f"❌ 在 {pic_folder} 文件夹中未找到图像文件")
        return

    print(f"📁 发现 {len(image_files)} 个图像文件")

    all_questions = []

    for image_path in image_paths:
        questions = parser.process_image(image_path)
        if questions:
            parser.format_output(questions)
            all_questions.extend(questions)

    if all_questions:
        print(f"\n🎉 总共处理了 {len(all_questions)} 道题目")
    else:
        print("❌ 未解析出任何题目")

if __name__ == "__main__":
    main()
