# OCRFlux API 测试指南

## 服务器信息
- **服务器地址**: `http://baizh.eu.cc:8002`
- **模型名称**: `models/`
- **API端点**: `/v1/chat/completions`
- **兼容性**: 完全兼容 OpenAI API 格式

## 测试结果 ✅

经过测试，OCRFlux服务器运行正常，支持以下功能：

1. ✅ **文本补全** - 纯文本对话功能正常
2. ✅ **多模态处理** - 图像+文本输入功能正常
3. ✅ **OCR功能** - 图像转文本功能正常
4. ✅ **OpenAI API兼容** - 完全兼容OpenAI API格式

## 快速测试

### 1. Python测试脚本
```bash
python3 quick_test.py
```

### 2. Bash/curl测试脚本
```bash
bash test_curl.sh
```

### 3. 完整功能测试
```bash
python3 test_ocrflux_api.py
```

## API使用示例

### Python示例 (使用requests)

```python
import requests
import base64

# 服务器配置
BASE_URL = "http://baizh.eu.cc:8002"
MODEL = "models/"

# 1. 文本补全
def text_completion(prompt):
    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    response = requests.post(f"{BASE_URL}/v1/chat/completions", json=payload)
    return response.json()["choices"][0]["message"]["content"]

# 2. OCR功能
def ocr_image(image_path):
    # 编码图像
    with open(image_path, "rb") as img_file:
        image_b64 = base64.b64encode(img_file.read()).decode('utf-8')
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "请将这个页面转换为markdown格式"},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 4096
    }
    
    response = requests.post(f"{BASE_URL}/v1/chat/completions", json=payload)
    return response.json()["choices"][0]["message"]["content"]

# 使用示例
result = text_completion("请介绍OCR技术")
print(result)

# OCR示例（需要图像文件）
# ocr_result = ocr_image("document.png")
# print(ocr_result)
```

### 使用OpenAI SDK

```python
from openai import OpenAI
import base64

# 配置客户端
client = OpenAI(
    api_key="dummy-key",  # OCRFlux不需要真实API key
    base_url="http://baizh.eu.cc:8002/v1"
)

# 文本补全
response = client.chat.completions.create(
    model="models/",
    messages=[{"role": "user", "content": "Hello!"}]
)
print(response.choices[0].message.content)

# OCR功能
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# image_base64 = encode_image("document.png")
# response = client.chat.completions.create(
#     model="models/",
#     messages=[
#         {
#             "role": "user",
#             "content": [
#                 {"type": "text", "text": "Convert to markdown"},
#                 {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
#             ]
#         }
#     ]
# )
```

### curl示例

```bash
# 文本补全
curl -X POST "http://baizh.eu.cc:8002/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "models/",
    "messages": [{"role": "user", "content": "Hello!"}],
    "temperature": 0.1
  }'

# OCR功能（需要先将图像转换为base64）
IMAGE_B64=$(base64 -w 0 your_image.png)

curl -X POST "http://baizh.eu.cc:8002/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "models/",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "Convert to markdown"},
          {"type": "image_url", "image_url": {"url": "data:image/png;base64,'$IMAGE_B64'"}}
        ]
      }
    ],
    "temperature": 0.0
  }'
```

## 响应格式

OCRFlux返回的响应格式完全兼容OpenAI API：

```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "models/",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "响应内容"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 200,
    "total_tokens": 300
  }
}
```

## OCR特殊响应格式

对于OCR任务，OCRFlux可能返回JSON格式的结构化数据：

```json
{
  "primary_language": "zh",
  "is_rotation_valid": true,
  "rotation_correction": 0,
  "is_table": false,
  "is_diagram": false,
  "natural_text": "转换后的markdown文本内容..."
}
```

## 注意事项

1. **模型名称**: 使用 `"models/"` 而不是 `"ChatDOC/OCRFlux-3B"`
2. **超时设置**: OCR任务可能需要较长时间，建议设置60-120秒超时
3. **图像格式**: 支持PNG、JPG、JPEG等常见格式
4. **图像大小**: 建议图像最长边不超过1024像素以获得最佳性能
5. **并发限制**: 根据服务器配置调整并发请求数量

## 故障排除

如果遇到问题，请检查：

1. 服务器是否正在运行：`curl http://baizh.eu.cc:8002/health`
2. 模型是否正确加载：`curl http://baizh.eu.cc:8002/v1/models`
3. 网络连接是否正常
4. 请求格式是否正确

## 文件说明

- `quick_test.py` - 快速功能测试脚本
- `test_ocrflux_api.py` - 完整API测试脚本
- `test_curl.sh` - Bash/curl测试脚本
- `check_models.py` - 检查可用模型脚本
- `sample_ocrflux_request.py` - 生成的示例代码
