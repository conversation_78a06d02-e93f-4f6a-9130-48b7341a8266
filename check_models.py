#!/usr/bin/env python3
"""
检查OCRFlux服务器可用模型
"""

import requests
import json

BASE_URL = "http://baizh.eu.cc:8002"

def check_models():
    """检查可用模型"""
    print("🔍 检查OCRFlux服务器可用模型...")
    print(f"🌐 服务器: {BASE_URL}")
    print("=" * 50)
    
    # 1. 尝试获取模型列表
    try:
        models_url = f"{BASE_URL}/v1/models"
        response = requests.get(models_url, timeout=10)
        
        if response.status_code == 200:
            models_data = response.json()
            print("✅ 成功获取模型列表:")
            print(json.dumps(models_data, indent=2, ensure_ascii=False))
            
            if 'data' in models_data:
                available_models = [model['id'] for model in models_data['data']]
                print(f"\n📋 可用模型: {available_models}")
                return available_models
        else:
            print(f"⚠️ 获取模型列表失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
    
    except Exception as e:
        print(f"❌ 请求模型列表失败: {e}")
    
    # 2. 尝试一些常见的模型名称
    print("\n🧪 尝试常见模型名称...")
    
    common_models = [
        "models",  # 你在命令行中使用的参数
        "ChatDOC/OCRFlux-3B",
        "OCRFlux-3B",
        "ocrflux",
        "default"
    ]
    
    api_endpoint = f"{BASE_URL}/v1/chat/completions"
    
    for model_name in common_models:
        print(f"🔍 测试模型: {model_name}")
        
        payload = {
            "model": model_name,
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 1,
            "temperature": 0.0
        }
        
        try:
            response = requests.post(api_endpoint, json=payload, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 模型 '{model_name}' 可用!")
                return [model_name]
            elif response.status_code == 404:
                error_data = response.json()
                if "does not exist" in error_data.get("message", ""):
                    print(f"❌ 模型 '{model_name}' 不存在")
                else:
                    print(f"⚠️ 模型 '{model_name}' 返回404: {error_data}")
            else:
                print(f"⚠️ 模型 '{model_name}' 返回状态码: {response.status_code}")
                print(f"响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 测试模型 '{model_name}' 失败: {e}")
    
    return []

def test_server_info():
    """获取服务器信息"""
    print("\n🔍 检查服务器信息...")
    
    # 尝试一些常见的信息端点
    endpoints = [
        "/health",
        "/v1/models",
        "/docs",
        "/openapi.json",
        "/"
    ]
    
    for endpoint in endpoints:
        url = f"{BASE_URL}{endpoint}"
        try:
            response = requests.get(url, timeout=5)
            print(f"📡 {endpoint}: HTTP {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"   JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)[:300]}...")
                    except:
                        print(f"   响应: {response.text[:200]}...")
                else:
                    print(f"   响应: {response.text[:200]}...")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

if __name__ == "__main__":
    available_models = check_models()
    test_server_info()
    
    if available_models:
        print(f"\n🎉 找到可用模型: {available_models}")
        print("💡 请在测试脚本中使用这些模型名称")
    else:
        print("\n❌ 未找到可用模型")
        print("💡 请检查:")
        print("1. 服务器是否正确启动")
        print("2. 模型是否正确加载")
        print("3. 服务器配置是否正确")
