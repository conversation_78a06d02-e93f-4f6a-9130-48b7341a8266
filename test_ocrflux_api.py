#!/usr/bin/env python3
"""
OCRFlux API测试脚本
兼容OpenAI API格式，用于测试部署在baizh.eu.cc:8002的OCRFlux服务器
"""

import asyncio
import base64
import json
import time
from io import BytesIO
from pathlib import Path
from typing import Optional, Dict, Any

import requests
from PIL import Image


class OCRFluxAPIClient:
    """OCRFlux API客户端，兼容OpenAI API格式"""
    
    def __init__(self, base_url: str = "http://baizh.eu.cc:8002", model: str = "models/"):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.completion_url = f"{self.base_url}/v1/chat/completions"
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图像文件编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def create_test_image(self, width: int = 100, height: int = 100) -> str:
        """创建一个测试图像并返回base64编码"""
        image = Image.new('RGB', (width, height), color='white')
        buffered = BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    def test_connection(self) -> bool:
        """测试服务器连接"""
        try:
            # 尝试访问根路径或健康检查端点
            response = requests.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200
        except:
            try:
                # 如果没有健康检查端点，尝试发送一个简单的请求
                test_payload = {
                    "model": self.model,
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1,
                    "temperature": 0.0
                }
                response = requests.post(self.completion_url, json=test_payload, timeout=10)
                return response.status_code in [200, 400, 422]  # 400/422可能是参数错误但服务器在线
            except:
                return False
    
    def page_to_markdown(self, image_path: str, temperature: float = 0.0) -> Optional[Dict[str, Any]]:
        """
        页面转Markdown功能测试
        
        Args:
            image_path: 图像文件路径
            temperature: 温度参数
            
        Returns:
            API响应结果
        """
        try:
            image_base64 = self.encode_image_to_base64(image_path)
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text", 
                                "text": "Convert this page to markdown format. Return the result as JSON with 'natural_text' field containing the markdown content."
                            },
                            {
                                "type": "image_url", 
                                "image_url": {"url": f"data:image/png;base64,{image_base64}"}
                            }
                        ]
                    }
                ],
                "temperature": temperature,
                "max_tokens": 4096
            }
            
            response = requests.post(self.completion_url, json=payload, timeout=60)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"页面转Markdown测试失败: {e}")
            return None
    
    def text_only_test(self, prompt: str = "Hello, how are you?", temperature: float = 0.0) -> Optional[Dict[str, Any]]:
        """
        纯文本测试
        
        Args:
            prompt: 测试提示词
            temperature: 温度参数
            
        Returns:
            API响应结果
        """
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": temperature,
                "max_tokens": 1024
            }
            
            response = requests.post(self.completion_url, json=payload, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"纯文本测试失败: {e}")
            return None
    
    def multimodal_test_with_dummy_image(self, text_prompt: str = "Describe this image") -> Optional[Dict[str, Any]]:
        """
        使用虚拟图像的多模态测试
        
        Args:
            text_prompt: 文本提示
            
        Returns:
            API响应结果
        """
        try:
            # 创建一个简单的测试图像
            test_image_base64 = self.create_test_image()
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": text_prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{test_image_base64}"}}
                        ]
                    }
                ],
                "temperature": 0.0,
                "max_tokens": 1024
            }
            
            response = requests.post(self.completion_url, json=payload, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"多模态测试失败: {e}")
            return None


def run_comprehensive_tests():
    """运行全面的API测试"""
    print("🚀 开始OCRFlux API测试...")
    print(f"📡 服务器地址: http://baizh.eu.cc:8002")
    print("=" * 60)
    
    client = OCRFluxAPIClient()
    
    # 1. 连接测试
    print("1️⃣ 测试服务器连接...")
    if client.test_connection():
        print("✅ 服务器连接成功!")
    else:
        print("❌ 服务器连接失败!")
        print("请检查:")
        print("- 服务器是否正在运行")
        print("- 网络连接是否正常")
        print("- 端口8002是否开放")
        return
    
    print()
    
    # 2. 纯文本测试
    print("2️⃣ 测试纯文本API调用...")
    text_result = client.text_only_test("请用中文回答：你是什么模型？")
    if text_result:
        print("✅ 纯文本API调用成功!")
        print(f"📝 响应: {text_result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')[:200]}...")
    else:
        print("❌ 纯文本API调用失败!")
    
    print()
    
    # 3. 多模态测试（虚拟图像）
    print("3️⃣ 测试多模态API调用（虚拟图像）...")
    multimodal_result = client.multimodal_test_with_dummy_image("请描述这个图像的内容")
    if multimodal_result:
        print("✅ 多模态API调用成功!")
        print(f"📝 响应: {multimodal_result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')[:200]}...")
    else:
        print("❌ 多模态API调用失败!")
    
    print()
    
    # 4. 如果有测试图像文件，进行OCR测试
    test_image_paths = ["test.png", "test.jpg", "test.jpeg", "sample.png", "sample.jpg"]
    test_image_path = None
    
    for path in test_image_paths:
        if Path(path).exists():
            test_image_path = path
            break
    
    if test_image_path:
        print(f"4️⃣ 测试OCR功能（使用图像: {test_image_path}）...")
        ocr_result = client.page_to_markdown(test_image_path)
        if ocr_result:
            print("✅ OCR功能测试成功!")
            content = ocr_result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')
            print(f"📝 OCR结果: {content[:300]}...")
        else:
            print("❌ OCR功能测试失败!")
    else:
        print("4️⃣ 跳过OCR测试（未找到测试图像文件）")
        print("💡 提示: 可以放置test.png、test.jpg等图像文件进行OCR测试")
    
    print()
    print("🎉 测试完成!")
    print("=" * 60)


def create_sample_request_script():
    """创建示例请求脚本"""
    sample_code = '''
# OCRFlux API使用示例
import requests
import base64

# 服务器配置
BASE_URL = "http://baizh.eu.cc:8002"
MODEL = "ChatDOC/OCRFlux-3B"

# 编码图像
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# 发送OCR请求
def ocr_request(image_path):
    image_base64 = encode_image(image_path)
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Convert this page to markdown format."},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                ]
            }
        ],
        "temperature": 0.0
    }
    
    response = requests.post(f"{BASE_URL}/v1/chat/completions", json=payload)
    return response.json()

# 使用示例
# result = ocr_request("your_image.png")
# print(result["choices"][0]["message"]["content"])
'''
    
    with open("sample_ocrflux_request.py", "w", encoding="utf-8") as f:
        f.write(sample_code)
    
    print("📄 已创建示例请求脚本: sample_ocrflux_request.py")


if __name__ == "__main__":
    run_comprehensive_tests()
    print()
    create_sample_request_script()
