#!/bin/bash

# OCRFlux API curl测试脚本
# 服务器: baizh.eu.cc:8002

BASE_URL="http://baizh.eu.cc:8002"
MODEL="models/"
API_ENDPOINT="${BASE_URL}/v1/chat/completions"

echo "🚀 OCRFlux API curl测试"
echo "🌐 服务器: $BASE_URL"
echo "🤖 模型: $MODEL"
echo "=" | tr -d '\n'; for i in {1..50}; do echo -n "="; done; echo

# 1. 测试服务器连接
echo "1️⃣ 测试服务器连接..."

response=$(curl -s -w "%{http_code}" -o /tmp/curl_response.txt \
  -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [{"role": "user", "content": "test"}],
    "max_tokens": 1,
    "temperature": 0.0
  }' \
  --connect-timeout 10 \
  --max-time 30)

http_code="${response: -3}"

if [ "$http_code" = "200" ] || [ "$http_code" = "400" ] || [ "$http_code" = "422" ]; then
    echo "✅ 服务器连接成功! (HTTP $http_code)"
else
    echo "❌ 服务器连接失败! (HTTP $http_code)"
    echo "响应内容:"
    cat /tmp/curl_response.txt
    echo
    echo "请检查:"
    echo "- 服务器是否正在运行"
    echo "- 网络连接是否正常"
    echo "- 端口8002是否开放"
    exit 1
fi

echo

# 2. 测试文本补全
echo "2️⃣ 测试文本补全..."

curl -s -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {"role": "user", "content": "请用中文简单介绍OCR技术，限制在100字以内。"}
    ],
    "temperature": 0.1,
    "max_tokens": 200
  }' \
  --connect-timeout 10 \
  --max-time 60 | jq -r '.choices[0].message.content // "解析失败"' > /tmp/text_result.txt

if [ $? -eq 0 ] && [ -s /tmp/text_result.txt ]; then
    echo "✅ 文本补全成功!"
    echo "📄 回答:"
    cat /tmp/text_result.txt
    echo
else
    echo "❌ 文本补全失败!"
fi

echo

# 3. 创建测试图像的base64编码
echo "3️⃣ 测试多模态功能..."

# 创建一个简单的测试图像 (1x1像素白色PNG)
test_image_b64="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

curl -s -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "请描述这个图像并转换为markdown格式。"},
          {"type": "image_url", "image_url": {"url": "data:image/png;base64,'$test_image_b64'"}}
        ]
      }
    ],
    "temperature": 0.0,
    "max_tokens": 500
  }' \
  --connect-timeout 10 \
  --max-time 120 | jq -r '.choices[0].message.content // "解析失败"' > /tmp/multimodal_result.txt

if [ $? -eq 0 ] && [ -s /tmp/multimodal_result.txt ]; then
    echo "✅ 多模态测试成功!"
    echo "📄 结果:"
    cat /tmp/multimodal_result.txt
    echo
else
    echo "❌ 多模态测试失败!"
fi

echo

# 4. 如果有图像文件，测试OCR功能
echo "4️⃣ 检查OCR功能..."

# 查找当前目录下的图像文件
image_files=($(find . -maxdepth 1 -type f \( -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" \) | head -2))

if [ ${#image_files[@]} -gt 0 ]; then
    for image_file in "${image_files[@]}"; do
        echo "📸 测试图像: $image_file"
        
        # 将图像转换为base64
        if command -v base64 >/dev/null 2>&1; then
            image_b64=$(base64 -w 0 "$image_file" 2>/dev/null || base64 "$image_file" | tr -d '\n')
            
            if [ -n "$image_b64" ]; then
                curl -s -X POST "$API_ENDPOINT" \
                  -H "Content-Type: application/json" \
                  -d '{
                    "model": "'$MODEL'",
                    "messages": [
                      {
                        "role": "user",
                        "content": [
                          {"type": "text", "text": "请将这个页面转换为markdown格式，保持原有的结构和内容。"},
                          {"type": "image_url", "image_url": {"url": "data:image/png;base64,'$image_b64'"}}
                        ]
                      }
                    ],
                    "temperature": 0.0,
                    "max_tokens": 4096
                  }' \
                  --connect-timeout 10 \
                  --max-time 180 | jq -r '.choices[0].message.content // "解析失败"' > "ocr_result_$(basename "$image_file").md"
                
                if [ $? -eq 0 ] && [ -s "ocr_result_$(basename "$image_file").md" ]; then
                    echo "✅ OCR测试成功!"
                    echo "💾 结果已保存到: ocr_result_$(basename "$image_file").md"
                    echo "📄 前200字符预览:"
                    head -c 200 "ocr_result_$(basename "$image_file").md"
                    echo "..."
                else
                    echo "❌ OCR测试失败!"
                fi
            else
                echo "❌ 无法编码图像文件: $image_file"
            fi
        else
            echo "❌ 系统缺少base64命令"
        fi
        echo
    done
else
    echo "💡 未找到图像文件，跳过OCR测试"
    echo "提示: 在当前目录放置.png、.jpg或.jpeg文件可以测试OCR功能"
fi

echo "🎉 测试完成!"

# 清理临时文件
rm -f /tmp/curl_response.txt /tmp/text_result.txt /tmp/multimodal_result.txt

echo
echo "📋 测试总结:"
echo "- 如果所有测试都成功，说明OCRFlux API工作正常"
echo "- 可以使用相同的API格式集成到你的应用中"
echo "- API完全兼容OpenAI格式，可以使用OpenAI SDK"
